import sys
sys.path.append('.')
from app import get_db_connection

try:
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # Check what portfolio data exists for user 3 (the one we added test image to)
    cursor.execute('''
        SELECT id, project_title, project_role, project_description, 
               project_image_filename, project_image_mimetype,
               CASE WHEN project_image_data IS NOT NULL THEN 'YES' ELSE 'NO' END as has_image_data
        FROM approve_genius 
        WHERE id = 3
    ''')
    result = cursor.fetchone()
    
    if result:
        print('Portfolio data for user 3:')
        print(f'  ID: {result["id"]}')
        print(f'  Title: {result["project_title"]}')
        print(f'  Role: {result["project_role"]}')
        print(f'  Description: {result["project_description"]}')
        print(f'  Image filename: {result["project_image_filename"]}')
        print(f'  Image MIME type: {result["project_image_mimetype"]}')
        print(f'  Has image data: {result["has_image_data"]}')
        
        # Test if this would show an image in the template
        if result["project_image_filename"]:
            print(f'\n✅ This portfolio SHOULD show an image!')
            print(f'   Image URL would be: /api/portfolio-image/{result["id"]}')
        else:
            print(f'\n❌ This portfolio will NOT show an image (no filename)')
    else:
        print('No portfolio data found for user 3')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'Error: {e}')
