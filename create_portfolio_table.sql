-- <PERSON><PERSON>t to create new portfolio table for link-based portfolios
-- Run this in phpMyAdmin or MySQL command line

USE giggenius;

-- Create new portfolio table
CREATE TABLE IF NOT EXISTS portfolios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    genius_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    project_url VARCHAR(500) NOT NULL,
    technologies VARCHAR(500),
    project_type ENUM('web_development', 'mobile_app', 'design', 'data_science', 'other') DEFAULT 'other',
    status ENUM('published', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (genius_id) REFERENCES approve_genius(id) ON DELETE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_genius_id (genius_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Verify table was created
DESCRIBE portfolios;

-- Check table structure
SHOW CREATE TABLE portfolios;

-- Optional: Check if table exists and show sample data structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'giggenius' 
AND TABLE_NAME = 'portfolios'
ORDER BY ORDINAL_POSITION;
