import sys
sys.path.append('.')
from app import get_db_connection

try:
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # Check what portfolio data exists
    cursor.execute('''
        SELECT id, project_title, project_image_filename, project_image_mimetype, 
               CASE WHEN project_image_data IS NOT NULL THEN 'YES' ELSE 'NO' END as has_image_data
        FROM approve_genius 
        WHERE project_title IS NOT NULL
    ''')
    results = cursor.fetchall()
    
    print('Current portfolio data:')
    for row in results:
        print(f'  User {row["id"]}: "{row["project_title"]}"')
        print(f'    - Filename: {row["project_image_filename"]}')
        print(f'    - MIME Type: {row["project_image_mimetype"]}')
        print(f'    - Has Image Data: {row["has_image_data"]}')
        print()
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'Error: {e}')
