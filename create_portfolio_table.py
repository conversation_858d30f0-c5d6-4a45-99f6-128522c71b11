#!/usr/bin/env python3
"""
Script to create the new portfolio table
"""
import sys
sys.path.append('.')
from app import get_db_connection

def create_portfolio_table():
    """Create the new portfolio table"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("Creating portfolio table...")
        
        # Create portfolio table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS portfolios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            genius_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            project_url VARCHAR(500) NOT NULL,
            technologies VARCHAR(500),
            project_type ENUM('web_development', 'mobile_app', 'design', 'data_science', 'other') DEFAULT 'other',
            status ENUM('published', 'draft') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIG<PERSON> KEY (genius_id) REFERENCES approve_genius(id) ON DELETE CASCADE,
            INDEX idx_genius_id (genius_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        )
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("✅ Portfolio table created successfully!")
        
        # Verify table structure
        cursor.execute("DESCRIBE portfolios")
        columns = cursor.fetchall()
        
        print("\n📋 Table structure:")
        for column in columns:
            print(f"  - {column[0]}: {column[1]} {'NULL' if column[2] == 'YES' else 'NOT NULL'}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error creating portfolio table: {e}")

if __name__ == "__main__":
    create_portfolio_table()
