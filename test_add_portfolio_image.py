import sys
import base64
sys.path.append('.')
from app import get_db_connection

# Create a simple test image (1x1 pixel PNG)
test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
test_image_data = base64.b64decode(test_image_base64)

try:
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Update the first portfolio entry (user 3) with test image data
    update_query = """
        UPDATE approve_genius 
        SET project_image_data = %s, 
            project_image_filename = %s, 
            project_image_mimetype = %s,
            project_content = %s
        WHERE id = 3 AND project_title IS NOT NULL
    """
    
    # Add some content with the image
    content_with_image = f'<div class="content-block"><img src="data:image/png;base64,{test_image_base64}" alt="Test Image" style="width: 100%; height: auto;"></div>'
    
    cursor.execute(update_query, (
        test_image_data,
        "test_portfolio_image.png", 
        "image/png",
        content_with_image
    ))
    
    conn.commit()
    print("✅ Added test image to portfolio entry for user 3")
    
    # Verify the update
    cursor.execute("""
        SELECT project_title, project_image_filename, 
               CASE WHEN project_image_data IS NOT NULL THEN 'YES' ELSE 'NO' END as has_image_data
        FROM approve_genius 
        WHERE id = 3
    """)
    result = cursor.fetchone()
    if result:
        print(f"Portfolio: {result[0]}")
        print(f"Image filename: {result[1]}")
        print(f"Has image data: {result[2]}")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
